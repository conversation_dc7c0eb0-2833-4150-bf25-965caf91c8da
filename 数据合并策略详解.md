# 数据合并策略详解

## 🎯 问题背景

在实现下拉刷新功能时，需要处理一个重要问题：**当有新的数据块加载进来时，如何处理老的数据块？**

这个问题涉及到：
1. **数据去重**：新数据可能与现有数据重复
2. **数据排序**：新数据应该按时间顺序正确插入
3. **内存管理**：避免数据量过大导致内存问题
4. **用户体验**：保持界面的流畅性和数据的一致性

## 🔧 解决方案

### 1. 统一的数据合并接口

```swift
private func mergeNewFeedsWithExisting(newFeeds: [FeedPost], isRefresh: Bool) -> [FeedPost] {
    if isRefresh {
        // 下拉刷新时的合并策略
        return mergeRefreshData(newFeeds: newFeeds)
    } else {
        // 分页加载时的合并策略
        return mergePaginationData(newFeeds: newFeeds)
    }
}
```

### 2. 下拉刷新的数据合并策略

#### 核心逻辑
```swift
private func mergeRefreshData(newFeeds: [FeedPost]) -> [FeedPost] {
    var existingFeeds = self.feeds
    var mergedFeeds: [FeedPost] = []
    
    // 1. 创建现有数据的ID集合，用于快速查找
    let existingIds = Set(existingFeeds.map { $0.id })
    
    // 2. 处理新数据
    for newFeed in newFeeds {
        if !existingIds.contains(newFeed.id) {
            // 新数据，直接添加
            mergedFeeds.append(newFeed)
        } else {
            // 重复数据，用新数据更新旧数据
            if let index = existingFeeds.firstIndex(where: { $0.id == newFeed.id }) {
                existingFeeds[index] = newFeed
            }
        }
    }
    
    // 3. 添加现有数据
    mergedFeeds.append(contentsOf: existingFeeds)
    
    // 4. 按时间排序
    mergedFeeds.sort { feed1, feed2 in
        // 使用 publishTime 字段排序
        let time1 = feed1.publishTime
        let time2 = feed2.publishTime
        
        // 尝试转换为Date比较
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        
        if let date1 = formatter.date(from: time1), 
           let date2 = formatter.date(from: time2) {
            return date1 > date2
        }
        
        // 字符串比较作为备选
        if time1 != time2 {
            return time1 > time2
        }
        
        // ID比较作为最后手段
        return feed1.id > feed2.id
    }
    
    // 5. 限制数据量
    let maxFeedsCount = 200
    if mergedFeeds.count > maxFeedsCount {
        mergedFeeds = Array(mergedFeeds.prefix(maxFeedsCount))
    }
    
    return mergedFeeds
}
```

#### 处理步骤详解

1. **数据去重**
   - 使用 `Set<Int>` 存储现有数据的ID，实现O(1)的查找效率
   - 新数据中的重复项会更新对应的旧数据

2. **数据更新**
   - 如果新数据中有与旧数据相同ID的项目，用新数据替换旧数据
   - 这样可以获取最新的点赞数、评论数等动态信息

3. **时间排序**
   - 优先使用 `publishTime` 字段转换为 `Date` 进行比较
   - 如果时间解析失败，使用字符串比较（ISO格式可直接比较）
   - 最后使用ID比较作为备选方案

4. **内存控制**
   - 限制最大数据量为200条
   - 保留最新的数据，删除较旧的数据

### 3. 分页加载的数据合并策略

```swift
private func mergePaginationData(newFeeds: [FeedPost]) -> [FeedPost] {
    var mergedFeeds = self.feeds
    
    // 去重：检查新数据中是否有与现有数据重复的
    let existingIds = Set(mergedFeeds.map { $0.id })
    let uniqueNewFeeds = newFeeds.filter { !existingIds.contains($0.id) }
    
    // 直接追加到末尾
    mergedFeeds.append(contentsOf: uniqueNewFeeds)
    
    return mergedFeeds
}
```

#### 分页加载特点
- **简单追加**：新数据直接添加到现有数据末尾
- **去重处理**：过滤掉重复的数据
- **保持顺序**：维持原有的分页顺序

## 📊 不同场景的处理方式

### 场景1：下拉刷新
```
现有数据: [Post3, Post2, Post1]  (按时间降序)
新数据:   [Post5, Post4, Post2]  (Post2是重复的)

处理结果: [Post5, Post4, Post3, Post2(更新), Post1]
```

### 场景2：分页加载
```
现有数据: [Post5, Post4, Post3]  (第一页)
新数据:   [Post2, Post1, Post0]  (第二页)

处理结果: [Post5, Post4, Post3, Post2, Post1, Post0]
```

### 场景3：数据更新
```
现有数据: Post2 {likeCount: 10, commentCount: 5}
新数据:   Post2 {likeCount: 15, commentCount: 8}

处理结果: Post2 {likeCount: 15, commentCount: 8}  (使用新数据)
```

## 🎯 优势和特点

### 1. 性能优化
- **O(1)查找**：使用Set进行ID查找
- **批量操作**：一次性处理所有数据
- **内存控制**：限制数据总量

### 2. 数据一致性
- **去重保证**：确保没有重复数据
- **时间排序**：保持正确的时间顺序
- **状态同步**：新数据覆盖旧数据的状态

### 3. 用户体验
- **流畅刷新**：下拉刷新获取最新内容
- **无缝分页**：分页加载不影响现有数据
- **实时更新**：动态信息（点赞、评论）实时更新

## 🧪 测试建议

### 基本功能测试
1. 下拉刷新时新数据正确插入到顶部
2. 分页加载时新数据正确追加到底部
3. 重复数据被正确去重和更新

### 边界情况测试
1. 新数据全部重复的情况
2. 新数据全部为新内容的情况
3. 大量数据时的内存控制

### 性能测试
1. 大数据量时的合并性能
2. 频繁刷新时的响应速度
3. 内存使用情况监控

## 📝 总结

通过实现智能的数据合并策略，我们解决了新旧数据块处理的问题：

1. **下拉刷新**：智能合并，去重更新，时间排序
2. **分页加载**：简单追加，去重处理
3. **内存管理**：限制数据量，避免内存问题
4. **用户体验**：流畅的数据更新，一致的界面表现

这个方案确保了数据的准确性、界面的流畅性和良好的用户体验。
